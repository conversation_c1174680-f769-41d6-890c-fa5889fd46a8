const { config } = require('aws-sdk');
const { NudgeRule } = require('../src/nudgeEngine');
const daysToLive = config.nudgeTTLDays || 2;
class SleepMealTimingRule extends NudgeRule {
  constructor(params = {}) {
    super("sleep_meal_gap", "Encourage early dinner before sleep.");
    this.minGapHours = params.minGapHours || 4;
  }

  applies({ data }) {
    const lastMealTime = data.mealLogs?.date || null;
    const sleepTime = data.sleepLogs?.startTime || null;
    if (!lastMealTime || !sleepTime) return false;
    const gap = (new Date(sleepTime) - new Date(lastMealTime)) / (1000 * 60 * 60);
    return (gap > 0) && (gap < this.minGapHours);
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "sleep";
    const title = "Too Close to Bedtime?";
    const message = `Try to finish your last meal at least ${this.minGapHours} hours before bedtime.`;
    return getNudgeDocument(userId, category, title, message);
  }
}

function getNudgeDocument(userId, category, title, message) {
  const currentTime = new Date();
  const ttlDate = new Date(currentTime.getTime() + daysToLive * 24 * 60 * 60 * 1000);

  const doc = {
    userId,
    type: "nudge",
    content: {
      title,
      message,
    },
    timestamp: currentTime.toISOString(),
    category,
    priority: "medium",
    ttl: ttlDate.toISOString(),
    status: "open"
  };

  return doc;
}

module.exports = {
  SleepMealTimingRule,
};
